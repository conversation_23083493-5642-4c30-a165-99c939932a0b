using System;
using System.Threading.Tasks;
using System.Threading;

namespace Kiva_MIDI
{
    /// <summary>
    /// Handles pre-rendering of MIDI notes to textures after MIDI loading completes
    /// </summary>
    public class MIDIPreRenderer : IDisposable
    {
        private readonly Settings settings;
        private readonly RaylibRenderer renderer;
        private ChunkedTextureManager textureManager;
        private bool disposed = false;
        private CancellationTokenSource cancellationTokenSource;
        private Task preRenderTask;

        public bool IsPreRenderingActive { get; private set; } = false;
        public bool IsPreRenderComplete => textureManager?.IsPreRenderComplete ?? false;
        public double PreRenderProgress => textureManager?.PreRenderProgress ?? 0.0;

        // Events
        public event Action PreRenderStarted;
        public event Action PreRenderCompleted;
        public event Action<double> PreRenderProgressChanged;
        public event Action<string> PreRenderError;

        public MIDIPreRenderer(Settings settings, RaylibRenderer renderer)
        {
            this.settings = settings;
            this.renderer = renderer;
        }

        /// <summary>
        /// Initialize the pre-renderer for a new MIDI file
        /// </summary>
        public void Initialize(double midiLength, int screenWidth, int screenHeight)
        {
            if (disposed) return;

            // Cancel any existing pre-rendering
            CancelPreRendering();

            // Create new texture manager
            textureManager?.Dispose();
            textureManager = new ChunkedTextureManager(settings, renderer);
            textureManager.Initialize(midiLength, screenWidth, screenHeight);
        }

        /// <summary>
        /// Start pre-rendering MIDI notes to textures
        /// </summary>
        public void StartPreRendering(MIDIMemoryFile midiFile)
        {
            if (disposed || midiFile == null || IsPreRenderingActive) return;

            try
            {
                cancellationTokenSource = new CancellationTokenSource();
                IsPreRenderingActive = true;

                PreRenderStarted?.Invoke();

                // Start pre-rendering on a background thread
                preRenderTask = Task.Run(() => PreRenderWorker(midiFile, cancellationTokenSource.Token), cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                IsPreRenderingActive = false;
                PreRenderError?.Invoke($"Failed to start pre-rendering: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancel ongoing pre-rendering
        /// </summary>
        public void CancelPreRendering()
        {
            if (!IsPreRenderingActive) return;

            try
            {
                cancellationTokenSource?.Cancel();
                preRenderTask?.Wait(5000); // Wait up to 5 seconds for cancellation
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error cancelling pre-rendering: {ex.Message}");
            }
            finally
            {
                IsPreRenderingActive = false;
                cancellationTokenSource?.Dispose();
                cancellationTokenSource = null;
                preRenderTask = null;
            }
        }

        /// <summary>
        /// Render the pre-rendered textures at the current time
        /// </summary>
        public void RenderAtTime(double time, double timeScale)
        {
            if (disposed || !IsPreRenderComplete) return;

            textureManager?.RenderAtTime(time, timeScale);
        }

        /// <summary>
        /// Check if pre-rendering is available for the current MIDI file
        /// </summary>
        public bool CanUsePreRendering()
        {
            return textureManager != null && IsPreRenderComplete;
        }

        private void PreRenderWorker(MIDIMemoryFile midiFile, CancellationToken cancellationToken)
        {
            try
            {
                if (textureManager == null)
                {
                    PreRenderError?.Invoke("Texture manager not initialized");
                    return;
                }

                // Monitor progress and report updates
                var progressMonitor = Task.Run(async () =>
                {
                    double lastProgress = 0;
                    while (IsPreRenderingActive && !cancellationToken.IsCancellationRequested)
                    {
                        double currentProgress = textureManager.PreRenderProgress;
                        if (Math.Abs(currentProgress - lastProgress) > 0.01) // Report progress changes > 1%
                        {
                            lastProgress = currentProgress;
                            PreRenderProgressChanged?.Invoke(currentProgress);
                        }

                        await Task.Delay(100, cancellationToken); // Check progress every 100ms
                    }
                }, cancellationToken);

                // Perform the actual pre-rendering
                textureManager.PreRenderMIDI(midiFile);

                // Check if we were cancelled
                cancellationToken.ThrowIfCancellationRequested();

                // Pre-rendering completed successfully
                IsPreRenderingActive = false;
                PreRenderCompleted?.Invoke();
                PreRenderProgressChanged?.Invoke(1.0);

                // Stop progress monitoring
                progressMonitor.Wait(1000);
            }
            catch (OperationCanceledException)
            {
                // Pre-rendering was cancelled
                IsPreRenderingActive = false;
                Console.WriteLine("MIDI pre-rendering was cancelled");
            }
            catch (Exception ex)
            {
                IsPreRenderingActive = false;
                PreRenderError?.Invoke($"Pre-rendering failed: {ex.Message}");
                Console.WriteLine($"Error during MIDI pre-rendering: {ex.Message}");
            }
        }

        /// <summary>
        /// Get memory usage information for the pre-rendered textures
        /// </summary>
        public (int chunkCount, long estimatedMemoryMB) GetMemoryUsage()
        {
            if (textureManager == null) return (0, 0);

            // Estimate memory usage based on texture dimensions
            // Each chunk is textureWidth * CHUNK_HEIGHT * 4 bytes (RGBA)
            int chunkCount = (int)Math.Ceiling(textureManager.TotalMidiLength / 10.0); // 10 seconds per chunk
            long bytesPerChunk = textureManager.TextureWidth * 2048 * 4; // CHUNK_HEIGHT = 2048
            long totalBytes = chunkCount * bytesPerChunk;
            long totalMB = totalBytes / (1024 * 1024);

            return (chunkCount, totalMB);
        }

        /// <summary>
        /// Force cleanup of texture resources (useful for memory management)
        /// </summary>
        public void ClearTextures()
        {
            if (disposed) return;

            CancelPreRendering();
            textureManager?.Dispose();
            textureManager = null;
        }

        public void Dispose()
        {
            if (disposed) return;

            CancelPreRendering();
            textureManager?.Dispose();
            textureManager = null;
            disposed = true;
        }
    }
}
