<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Kiva_MIDI</RootNamespace>
    <AssemblyName>Kiva-MIDI</AssemblyName>
    <UseWPF>false</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Raylib-cs" Version="7.0.1" />
    <PackageReference Include="Sanford.Multimedia.Midi" Version="6.6.0" />
    <PackageReference Include="CSCore" Version="1.2.1.2" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    <PackageReference Include="PropertyChanged.Fody" Version="3.1.3" />
    <PackageReference Include="Fody" Version="6.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="**/*.cs" Exclude="bin/**;obj/**;MainWindow.xaml.cs;App.xaml.cs;Settings/AdvancedSettings.xaml.cs;Settings/ColorPaletteSettings.xaml.cs;Settings/GeneralSettings.xaml.cs;Settings/MIDILoaderSettings.xaml.cs;Settings/SoundfontSettings.xaml.cs;LoadingMidiForm.xaml.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Include="App.config" />
    <Content Include="packages\Raylib-cs.7.0.1\runtimes\win-x64\native\raylib.dll" Condition="Exists('packages\Raylib-cs.7.0.1\runtimes\win-x64\native\raylib.dll')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Notes.fx" />
    <EmbeddedResource Include="KeyboardBig.fx" />
    <EmbeddedResource Include="KeyboardSmall.fx" />
  </ItemGroup>

</Project>
