using System;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Simple test to verify Raylib-cs works
    /// </summary>
    public class TestRaylib
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Testing Raylib-cs...");
            
            try
            {
                Raylib.InitWindow(800, 600, "Raylib Test");
                
                while (!Raylib.WindowShouldClose())
                {
                    Raylib.BeginDrawing();
                    Raylib.ClearBackground(Color.BLACK);
                    
                    Raylib.DrawText("Raylib P/Invoke is working!", 10, 10, 20, Color.WHITE);
                    Raylib.DrawText("Press ESC to close", 10, 40, 16, Color.GRAY);
                    
                    Raylib.EndDrawing();
                }
                
                Raylib.CloseWindow();
                Console.WriteLine("Raylib test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Raylib test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
