using System;
using System.Collections.Generic;
using System.Linq;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Manages pre-rendered MIDI note textures in chunks for efficient memory usage and smooth scrolling
    /// </summary>
    public class ChunkedTextureManager : IDisposable
    {
        private struct TextureChunk
        {
            public RenderTexture2D renderTexture;
            public double startTime;
            public double endTime;
            public bool isLoaded;
            public int chunkIndex;
        }

        private readonly Settings settings;
        private readonly RaylibRenderer renderer;
        private List<TextureChunk> chunks = new List<TextureChunk>();
        private bool disposed = false;

        // Chunk configuration - reduced for better GPU compatibility
        private const int CHUNK_HEIGHT = 1024; // Height of each texture chunk in pixels (reduced from 2048)
        private const double CHUNK_TIME_SPAN = 5.0; // Time span covered by each chunk in seconds (reduced from 10.0)
        private int textureWidth;
        private int noteAreaHeight;
        private double totalMidiLength;
        private double pixelsPerSecond;

        // Current rendering state
        private double currentTime = 0;
        private double currentTimeScale = 1.0;
        private int activeChunkIndex = -1;
        private int textureCreationFailures = 0;
        private const int MAX_TEXTURE_FAILURES = 5;

        public bool IsPreRenderComplete { get; private set; } = false;
        public double PreRenderProgress { get; private set; } = 0.0;
        public bool HasTooManyFailures => textureCreationFailures >= MAX_TEXTURE_FAILURES;
        public double TotalMidiLength => totalMidiLength;
        public int TextureWidth => textureWidth;

        public ChunkedTextureManager(Settings settings, RaylibRenderer renderer)
        {
            this.settings = settings;
            this.renderer = renderer;
        }

        /// <summary>
        /// Initialize the chunked texture system for a MIDI file
        /// </summary>
        public void Initialize(double midiLength, int screenWidth, int screenHeight)
        {
            if (disposed) return;

            totalMidiLength = midiLength;

            // Clamp texture dimensions to reasonable limits
            textureWidth = Math.Max(256, Math.Min(2048, screenWidth));
            noteAreaHeight = (int)(screenHeight * 0.85f); // 85% for notes, 15% for keyboard

            // Calculate how many pixels per second we need based on the default time scale
            pixelsPerSecond = CHUNK_HEIGHT / CHUNK_TIME_SPAN;

            // Calculate number of chunks needed
            int numChunks = (int)Math.Ceiling(totalMidiLength / CHUNK_TIME_SPAN);

            // Limit the number of chunks to prevent excessive memory usage
            const int MAX_CHUNKS = 200; // Limit to ~16 minutes at 5s per chunk
            if (numChunks > MAX_CHUNKS)
            {
                Console.WriteLine($"Warning: MIDI file too long ({totalMidiLength:F1}s), limiting to {MAX_CHUNKS} chunks");
                numChunks = MAX_CHUNKS;
                totalMidiLength = MAX_CHUNKS * CHUNK_TIME_SPAN;
            }

            Console.WriteLine($"Initializing {numChunks} texture chunks ({textureWidth}x{CHUNK_HEIGHT} each)");
            Console.WriteLine($"Estimated memory usage: {(numChunks * textureWidth * CHUNK_HEIGHT * 4) / (1024 * 1024)}MB");

            // Clear existing chunks
            DisposeChunks();
            chunks.Clear();

            // Create chunk metadata (textures will be created during pre-rendering)
            for (int i = 0; i < numChunks; i++)
            {
                chunks.Add(new TextureChunk
                {
                    startTime = i * CHUNK_TIME_SPAN,
                    endTime = Math.Min((i + 1) * CHUNK_TIME_SPAN, totalMidiLength),
                    isLoaded = false,
                    chunkIndex = i
                });
            }

            IsPreRenderComplete = false;
            PreRenderProgress = 0.0;
        }

        /// <summary>
        /// Pre-render all MIDI notes to texture chunks
        /// </summary>
        public void PreRenderMIDI(MIDIMemoryFile midiFile)
        {
            if (disposed || midiFile == null) return;

            textureCreationFailures = 0;

            try
            {
                for (int chunkIndex = 0; chunkIndex < chunks.Count; chunkIndex++)
                {
                    PreRenderProgress = (double)chunkIndex / chunks.Count;
                    PreRenderChunk(midiFile, chunkIndex);

                    // Check if we have too many failures and should abort
                    if (HasTooManyFailures)
                    {
                        Console.WriteLine($"Aborting pre-rendering due to {textureCreationFailures} texture creation failures");
                        IsPreRenderComplete = false;
                        return;
                    }
                }

                // Only mark as complete if we have at least some successful chunks
                int successfulChunks = chunks.Count(c => c.isLoaded);
                if (successfulChunks > 0)
                {
                    IsPreRenderComplete = true;
                    PreRenderProgress = 1.0;
                    Console.WriteLine($"Pre-rendering completed with {successfulChunks}/{chunks.Count} successful chunks");
                }
                else
                {
                    Console.WriteLine("Pre-rendering failed - no chunks were successfully created");
                    IsPreRenderComplete = false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during MIDI pre-rendering: {ex.Message}");
                IsPreRenderComplete = false;
            }
        }

        private void PreRenderChunk(MIDIMemoryFile midiFile, int chunkIndex)
        {
            var chunk = chunks[chunkIndex];

            try
            {
                // Validate texture dimensions
                if (textureWidth <= 0 || textureWidth > 4096 || CHUNK_HEIGHT <= 0 || CHUNK_HEIGHT > 4096)
                {
                    Console.WriteLine($"Invalid texture dimensions: {textureWidth}x{CHUNK_HEIGHT}");
                    return;
                }

                // Create render texture for this chunk
                var renderTexture = Raylib.LoadRenderTexture(textureWidth, CHUNK_HEIGHT);

                // Check if texture creation was successful
                if (renderTexture.id == 0 || renderTexture.texture.id == 0)
                {
                    textureCreationFailures++;
                    Console.WriteLine($"Failed to create render texture for chunk {chunkIndex} (failure #{textureCreationFailures})");
                    return;
                }

                // Begin rendering to texture
                Raylib.BeginTextureMode(renderTexture);
                Raylib.ClearBackground(RaylibColor.BLANK); // Transparent background

                // Set up rendering parameters for this chunk
                double chunkStartTime = chunk.startTime;
                double chunkEndTime = chunk.endTime;
                double chunkDuration = chunkEndTime - chunkStartTime;

                // Render all notes that fall within this chunk's time range
                RenderNotesToChunk(midiFile, chunkStartTime, chunkEndTime, chunkDuration);

                // End texture rendering
                Raylib.EndTextureMode();

                // Update chunk with the rendered texture
                chunk.renderTexture = renderTexture;
                chunk.isLoaded = true;
                chunks[chunkIndex] = chunk;

                Console.WriteLine($"Successfully pre-rendered chunk {chunkIndex + 1}/{chunks.Count} ({chunkStartTime:F1}s - {chunkEndTime:F1}s)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error pre-rendering chunk {chunkIndex}: {ex.Message}");
            }
        }

        private void RenderNotesToChunk(MIDIMemoryFile midiFile, double startTime, double endTime, double duration)
        {
            if (midiFile?.Notes == null) return;

            // Set up color events for the chunk time range
            try
            {
                midiFile.SetColorEvents(startTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = midiFile.MidiNoteColors;
            if (colors == null) return;

            // Get keyboard layout from renderer
            var blackKeys = GetBlackKeysArray();
            var keyPositions = GetKeyPositions();

            // Count total notes to render for this chunk
            int totalNotes = 0;
            for (int k = 0; k < 256; k++)
            {
                if (midiFile.Notes[k] != null)
                {
                    foreach (var note in midiFile.Notes[k])
                    {
                        if (note.end >= startTime && note.start <= endTime)
                            totalNotes++;
                    }
                }
            }

            Console.WriteLine($"Rendering {totalNotes} notes for chunk {startTime:F1}s - {endTime:F1}s");

            // Render in batches to avoid overflow
            const int BATCH_SIZE = 500; // Reduced batch size to prevent overflow
            int notesRendered = 0;

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                notesRendered += RenderNotesForKeyToChunk(midiFile, k, startTime, endTime, duration, colors, keyPositions, BATCH_SIZE, notesRendered);
            }

            // Render black key notes second (foreground layer)
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                notesRendered += RenderNotesForKeyToChunk(midiFile, k, startTime, endTime, duration, colors, keyPositions, BATCH_SIZE, notesRendered);
            }

            Console.WriteLine($"Completed rendering {notesRendered} notes for chunk");
        }

        private int RenderNotesForKeyToChunk(MIDIMemoryFile midiFile, int keyIndex, double startTime, double endTime, double duration, NoteCol[] colors, (float left, float right)[] keyPositions, int batchSize, int currentBatchCount)
        {
            if (midiFile.Notes == null || keyIndex >= midiFile.Notes.Length)
                return 0;

            var notes = midiFile.Notes[keyIndex];
            if (notes == null || notes.Length == 0)
                return 0;

            var keyPos = keyPositions[keyIndex];
            if (keyPos.right <= keyPos.left) return 0; // Invalid key position

            int notesRendered = 0;
            int batchCount = currentBatchCount;

            // Find notes that intersect with this chunk's time range
            foreach (var note in notes)
            {
                // Check if note intersects with chunk time range
                if (note.end < startTime || note.start > endTime)
                    continue;

                // Check if we need to flush the batch to prevent overflow
                if (batchCount >= batchSize)
                {
                    // Force a flush by ending and beginning texture mode
                    Raylib.EndTextureMode();
                    System.Threading.Thread.Sleep(1); // Small delay to let GPU process
                    Raylib.BeginTextureMode(chunks[GetCurrentChunkIndex(startTime)].renderTexture);
                    batchCount = 0;
                }

                // Calculate note position within the chunk
                double noteStartInChunk = Math.Max(0, note.start - startTime);
                double noteEndInChunk = Math.Min(duration, note.end - startTime);

                if (noteEndInChunk <= noteStartInChunk) continue;

                // Convert time to Y coordinates (flipped - notes move upward)
                float noteTop = (float)(CHUNK_HEIGHT - (noteEndInChunk / duration * CHUNK_HEIGHT));
                float noteBottom = (float)(CHUNK_HEIGHT - (noteStartInChunk / duration * CHUNK_HEIGHT));

                // Ensure valid dimensions
                if (noteTop >= noteBottom || noteTop < 0 || noteBottom > CHUNK_HEIGHT) continue;

                // Get note colors
                var noteColor = colors[note.colorPointer];
                var colorLeft = ExtractColor(noteColor.rgba);
                var colorRight = ExtractColor(noteColor.rgba2);

                // Render note directly to the current render texture
                RenderNoteDirectly(keyPos.left, keyPos.right, noteTop, noteBottom, colorLeft, colorRight);

                notesRendered++;
                batchCount++;
            }

            return notesRendered;
        }

        private int GetCurrentChunkIndex(double time)
        {
            return (int)(time / CHUNK_TIME_SPAN);
        }

        /// <summary>
        /// Render the appropriate texture chunks for the current time and time scale
        /// </summary>
        public void RenderAtTime(double time, double timeScale)
        {
            if (!IsPreRenderComplete || disposed) return;

            currentTime = time;
            currentTimeScale = timeScale;

            // Find which chunk(s) we need to render
            int chunkIndex = (int)(time / CHUNK_TIME_SPAN);
            
            if (chunkIndex < 0 || chunkIndex >= chunks.Count) return;

            var chunk = chunks[chunkIndex];
            if (!chunk.isLoaded) return;

            // Calculate how much of the chunk to show based on current time and time scale
            double timeIntoChunk = time - chunk.startTime;
            double visibleDuration = Math.Min(timeScale, chunk.endTime - time);

            if (visibleDuration <= 0) return;

            // Calculate source rectangle (what part of the texture to draw)
            float sourceY = (float)(timeIntoChunk / CHUNK_TIME_SPAN * CHUNK_HEIGHT);
            float sourceHeight = (float)(visibleDuration / CHUNK_TIME_SPAN * CHUNK_HEIGHT);

            // Clamp to texture bounds
            sourceY = Math.Max(0, Math.Min(CHUNK_HEIGHT - 1, sourceY));
            sourceHeight = Math.Max(1, Math.Min(CHUNK_HEIGHT - sourceY, sourceHeight));

            var sourceRect = new Rectangle(0, sourceY, textureWidth, sourceHeight);
            var destRect = new Rectangle(0, 0, textureWidth, noteAreaHeight);

            // Draw the texture chunk
            Raylib.DrawTexturePro(chunk.renderTexture.texture, sourceRect, destRect, new Vector2(0, 0), 0, RaylibColor.WHITE);

            // If we need to show part of the next chunk too (for smooth scrolling)
            if (time + timeScale > chunk.endTime && chunkIndex + 1 < chunks.Count)
            {
                var nextChunk = chunks[chunkIndex + 1];
                if (nextChunk.isLoaded)
                {
                    double nextChunkVisibleTime = (time + timeScale) - nextChunk.startTime;
                    if (nextChunkVisibleTime > 0)
                    {
                        float nextSourceHeight = (float)(nextChunkVisibleTime / CHUNK_TIME_SPAN * CHUNK_HEIGHT);
                        nextSourceHeight = Math.Max(1, Math.Min(CHUNK_HEIGHT, nextSourceHeight));

                        var nextSourceRect = new Rectangle(0, 0, textureWidth, nextSourceHeight);
                        var nextDestY = noteAreaHeight - (float)(nextChunkVisibleTime / timeScale * noteAreaHeight);
                        var nextDestRect = new Rectangle(0, nextDestY, textureWidth, (float)(nextChunkVisibleTime / timeScale * noteAreaHeight));

                        Raylib.DrawTexturePro(nextChunk.renderTexture.texture, nextSourceRect, nextDestRect, new Vector2(0, 0), 0, RaylibColor.WHITE);
                    }
                }
            }
        }

        private bool[] GetBlackKeysArray()
        {
            // This should match the logic in RaylibRenderer
            bool[] blackKeys = new bool[257];
            for (int i = 0; i < blackKeys.Length; i++)
            {
                int n = i % 12;
                blackKeys[i] = n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
            }
            return blackKeys;
        }

        private (float left, float right)[] GetKeyPositions()
        {
            // Get the actual key positions from the renderer
            var positions = new (float left, float right)[257];

            for (int i = 0; i < 257; i++)
            {
                positions[i] = renderer.GetKeyPosition(i);
            }

            return positions;
        }

        private void RenderNoteDirectly(float left, float right, float top, float bottom, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Convert normalized coordinates to texture coordinates
            int noteLeft = (int)(left * textureWidth);
            int noteRight = (int)(right * textureWidth);
            int noteTop = (int)top;
            int noteBottom = (int)bottom;

            // Ensure valid dimensions
            int width = noteRight - noteLeft;
            int height = noteBottom - noteTop;

            if (width <= 0 || height <= 0 || noteLeft < 0 || noteRight > textureWidth ||
                noteTop < 0 || noteBottom > CHUNK_HEIGHT) return;

            // Render note with the same visual style as the original renderer
            RenderKivaMIDINoteDirect(noteLeft, noteTop, width, height, colorLeft, colorRight);
        }

        private void RenderKivaMIDINoteDirect(int x, int y, int width, int height, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Simplified rendering for better performance - reduce draw calls
            if (width <= 0 || height <= 0) return;

            // For very small notes, just draw a simple rectangle
            if (width <= 4 || height <= 4)
            {
                Raylib.DrawRectangle(x, y, width, height, colorLeft);
                return;
            }

            // Calculate border thickness (reduced for performance)
            int borderSize = Math.Max(1, Math.Min(2, width / 8)); // Adaptive border size

            // Draw shadow background (single color for performance)
            var shadowColor = new RaylibColor(
                (byte)Math.Max(0, (colorLeft.r + colorRight.r) / 2 * 0.3f),
                (byte)Math.Max(0, (colorLeft.g + colorRight.g) / 2 * 0.3f),
                (byte)Math.Max(0, (colorLeft.b + colorRight.b) / 2 * 0.3f),
                (byte)Math.Max(colorLeft.a, colorRight.a)
            );
            Raylib.DrawRectangle(x, y, width, height, shadowColor);

            // Draw bright inner area (single color for performance)
            if (width > borderSize * 2 && height > borderSize * 2)
            {
                var innerColor = new RaylibColor(
                    (byte)Math.Min(255, (colorLeft.r + colorRight.r) / 2 + 20),
                    (byte)Math.Min(255, (colorLeft.g + colorRight.g) / 2 + 20),
                    (byte)Math.Min(255, (colorLeft.b + colorRight.b) / 2 + 20),
                    (byte)Math.Max(colorLeft.a, colorRight.a)
                );

                int innerX = x + borderSize;
                int innerY = y + borderSize;
                int innerWidth = width - borderSize * 2;
                int innerHeight = height - borderSize * 2;

                Raylib.DrawRectangle(innerX, innerY, innerWidth, innerHeight, innerColor);
            }
        }

        private void DrawHorizontalGradientRectangleDirect(int x, int y, int width, int height, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // For small rectangles, just use solid color
            if (width <= 4)
            {
                Raylib.DrawRectangle(x, y, width, height, colorLeft);
                return;
            }

            // Draw gradient by drawing vertical strips
            int strips = Math.Min(width, 32);
            float stripWidth = (float)width / strips;

            for (int i = 0; i < strips; i++)
            {
                float t = strips > 1 ? (float)i / (strips - 1) : 0;
                var blendedColor = BlendColorsDirect(colorLeft, colorRight, t);

                int stripX = x + (int)(i * stripWidth);
                int stripW = (int)Math.Ceiling(stripWidth);

                if (stripX + stripW > x + width)
                    stripW = x + width - stripX;

                Raylib.DrawRectangle(stripX, y, stripW, height, blendedColor);
            }
        }

        private RaylibColor BlendColorsDirect(RaylibColor color1, RaylibColor color2, float t)
        {
            return new RaylibColor(
                (byte)(color1.r + (color2.r - color1.r) * t),
                (byte)(color1.g + (color2.g - color1.g) * t),
                (byte)(color1.b + (color2.b - color1.b) * t),
                (byte)(color1.a + (color2.a - color1.a) * t)
            );
        }

        private RaylibColor ExtractColor(uint rgba)
        {
            return new RaylibColor(
                (byte)((rgba >> 24) & 0xFF), // R
                (byte)((rgba >> 16) & 0xFF), // G
                (byte)((rgba >> 8) & 0xFF),  // B
                (byte)(rgba & 0xFF)          // A
            );
        }

        private void DisposeChunks()
        {
            foreach (var chunk in chunks)
            {
                if (chunk.isLoaded)
                {
                    Raylib.UnloadRenderTexture(chunk.renderTexture);
                }
            }
        }

        public void Dispose()
        {
            if (disposed) return;

            DisposeChunks();
            chunks.Clear();
            disposed = true;
        }
    }
}
