using System;
using System.Collections.Generic;
using System.Linq;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Manages pre-rendered MIDI note textures in chunks for efficient memory usage and smooth scrolling
    /// </summary>
    public class ChunkedTextureManager : IDisposable
    {
        private struct TextureChunk
        {
            public RenderTexture2D renderTexture;
            public double startTime;
            public double endTime;
            public bool isLoaded;
            public int chunkIndex;
        }

        private readonly Settings settings;
        private readonly RaylibRenderer renderer;
        private List<TextureChunk> chunks = new List<TextureChunk>();
        private bool disposed = false;

        // Chunk configuration
        private const int CHUNK_HEIGHT = 2048; // Height of each texture chunk in pixels
        private const double CHUNK_TIME_SPAN = 10.0; // Time span covered by each chunk in seconds
        private int textureWidth;
        private int noteAreaHeight;
        private double totalMidiLength;
        private double pixelsPerSecond;

        // Current rendering state
        private double currentTime = 0;
        private double currentTimeScale = 1.0;
        private int activeChunkIndex = -1;

        public bool IsPreRenderComplete { get; private set; } = false;
        public double PreRenderProgress { get; private set; } = 0.0;
        public double TotalMidiLength => totalMidiLength;
        public int TextureWidth => textureWidth;

        public ChunkedTextureManager(Settings settings, RaylibRenderer renderer)
        {
            this.settings = settings;
            this.renderer = renderer;
        }

        /// <summary>
        /// Initialize the chunked texture system for a MIDI file
        /// </summary>
        public void Initialize(double midiLength, int screenWidth, int screenHeight)
        {
            if (disposed) return;

            totalMidiLength = midiLength;
            textureWidth = screenWidth;
            noteAreaHeight = (int)(screenHeight * 0.85f); // 85% for notes, 15% for keyboard

            // Calculate how many pixels per second we need based on the default time scale
            pixelsPerSecond = CHUNK_HEIGHT / CHUNK_TIME_SPAN;

            // Calculate number of chunks needed
            int numChunks = (int)Math.Ceiling(totalMidiLength / CHUNK_TIME_SPAN);

            // Clear existing chunks
            DisposeChunks();
            chunks.Clear();

            // Create chunk metadata (textures will be created during pre-rendering)
            for (int i = 0; i < numChunks; i++)
            {
                chunks.Add(new TextureChunk
                {
                    startTime = i * CHUNK_TIME_SPAN,
                    endTime = Math.Min((i + 1) * CHUNK_TIME_SPAN, totalMidiLength),
                    isLoaded = false,
                    chunkIndex = i
                });
            }

            IsPreRenderComplete = false;
            PreRenderProgress = 0.0;
        }

        /// <summary>
        /// Pre-render all MIDI notes to texture chunks
        /// </summary>
        public void PreRenderMIDI(MIDIMemoryFile midiFile)
        {
            if (disposed || midiFile == null) return;

            try
            {
                for (int chunkIndex = 0; chunkIndex < chunks.Count; chunkIndex++)
                {
                    PreRenderProgress = (double)chunkIndex / chunks.Count;
                    PreRenderChunk(midiFile, chunkIndex);
                }

                IsPreRenderComplete = true;
                PreRenderProgress = 1.0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during MIDI pre-rendering: {ex.Message}");
                IsPreRenderComplete = false;
            }
        }

        private void PreRenderChunk(MIDIMemoryFile midiFile, int chunkIndex)
        {
            var chunk = chunks[chunkIndex];

            // Create render texture for this chunk
            var renderTexture = Raylib.LoadRenderTexture(textureWidth, CHUNK_HEIGHT);
            
            // Begin rendering to texture
            Raylib.BeginTextureMode(renderTexture);
            Raylib.ClearBackground(RaylibColor.BLANK); // Transparent background

            // Set up rendering parameters for this chunk
            double chunkStartTime = chunk.startTime;
            double chunkEndTime = chunk.endTime;
            double chunkDuration = chunkEndTime - chunkStartTime;

            // Render all notes that fall within this chunk's time range
            RenderNotesToChunk(midiFile, chunkStartTime, chunkEndTime, chunkDuration);

            // End texture rendering
            Raylib.EndTextureMode();

            // Update chunk with the rendered texture
            chunk.renderTexture = renderTexture;
            chunk.isLoaded = true;
            chunks[chunkIndex] = chunk;
        }

        private void RenderNotesToChunk(MIDIMemoryFile midiFile, double startTime, double endTime, double duration)
        {
            if (midiFile?.Notes == null) return;

            // Set up color events for the chunk time range
            try
            {
                midiFile.SetColorEvents(startTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = midiFile.MidiNoteColors;
            if (colors == null) return;

            // Get keyboard layout from renderer
            var blackKeys = GetBlackKeysArray();
            var keyPositions = GetKeyPositions();

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKeyToChunk(midiFile, k, startTime, endTime, duration, colors, keyPositions);
            }

            // Render black key notes second (foreground layer)
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKeyToChunk(midiFile, k, startTime, endTime, duration, colors, keyPositions);
            }
        }

        private void RenderNotesForKeyToChunk(MIDIMemoryFile midiFile, int keyIndex, double startTime, double endTime, double duration, NoteCol[] colors, (float left, float right)[] keyPositions)
        {
            if (midiFile.Notes == null || keyIndex >= midiFile.Notes.Length)
                return;

            var notes = midiFile.Notes[keyIndex];
            if (notes == null || notes.Length == 0)
                return;

            var keyPos = keyPositions[keyIndex];
            if (keyPos.right <= keyPos.left) return; // Invalid key position

            // Find notes that intersect with this chunk's time range
            foreach (var note in notes)
            {
                // Check if note intersects with chunk time range
                if (note.end < startTime || note.start > endTime)
                    continue;

                // Calculate note position within the chunk
                double noteStartInChunk = Math.Max(0, note.start - startTime);
                double noteEndInChunk = Math.Min(duration, note.end - startTime);

                if (noteEndInChunk <= noteStartInChunk) continue;

                // Convert time to Y coordinates (flipped - notes move upward)
                float noteTop = (float)(CHUNK_HEIGHT - (noteEndInChunk / duration * CHUNK_HEIGHT));
                float noteBottom = (float)(CHUNK_HEIGHT - (noteStartInChunk / duration * CHUNK_HEIGHT));

                // Ensure valid dimensions
                if (noteTop >= noteBottom || noteTop < 0 || noteBottom > CHUNK_HEIGHT) continue;

                // Get note colors
                var noteColor = colors[note.colorPointer];
                var colorLeft = ExtractColor(noteColor.rgba);
                var colorRight = ExtractColor(noteColor.rgba2);

                // Render note directly to the current render texture
                RenderNoteDirectly(keyPos.left, keyPos.right, noteTop, noteBottom, colorLeft, colorRight);
            }
        }

        /// <summary>
        /// Render the appropriate texture chunks for the current time and time scale
        /// </summary>
        public void RenderAtTime(double time, double timeScale)
        {
            if (!IsPreRenderComplete || disposed) return;

            currentTime = time;
            currentTimeScale = timeScale;

            // Find which chunk(s) we need to render
            int chunkIndex = (int)(time / CHUNK_TIME_SPAN);
            
            if (chunkIndex < 0 || chunkIndex >= chunks.Count) return;

            var chunk = chunks[chunkIndex];
            if (!chunk.isLoaded) return;

            // Calculate how much of the chunk to show based on current time and time scale
            double timeIntoChunk = time - chunk.startTime;
            double visibleDuration = Math.Min(timeScale, chunk.endTime - time);

            if (visibleDuration <= 0) return;

            // Calculate source rectangle (what part of the texture to draw)
            float sourceY = (float)(timeIntoChunk / CHUNK_TIME_SPAN * CHUNK_HEIGHT);
            float sourceHeight = (float)(visibleDuration / CHUNK_TIME_SPAN * CHUNK_HEIGHT);

            // Clamp to texture bounds
            sourceY = Math.Max(0, Math.Min(CHUNK_HEIGHT - 1, sourceY));
            sourceHeight = Math.Max(1, Math.Min(CHUNK_HEIGHT - sourceY, sourceHeight));

            var sourceRect = new Rectangle(0, sourceY, textureWidth, sourceHeight);
            var destRect = new Rectangle(0, 0, textureWidth, noteAreaHeight);

            // Draw the texture chunk
            Raylib.DrawTexturePro(chunk.renderTexture.texture, sourceRect, destRect, new Vector2(0, 0), 0, RaylibColor.WHITE);

            // If we need to show part of the next chunk too (for smooth scrolling)
            if (time + timeScale > chunk.endTime && chunkIndex + 1 < chunks.Count)
            {
                var nextChunk = chunks[chunkIndex + 1];
                if (nextChunk.isLoaded)
                {
                    double nextChunkVisibleTime = (time + timeScale) - nextChunk.startTime;
                    if (nextChunkVisibleTime > 0)
                    {
                        float nextSourceHeight = (float)(nextChunkVisibleTime / CHUNK_TIME_SPAN * CHUNK_HEIGHT);
                        nextSourceHeight = Math.Max(1, Math.Min(CHUNK_HEIGHT, nextSourceHeight));

                        var nextSourceRect = new Rectangle(0, 0, textureWidth, nextSourceHeight);
                        var nextDestY = noteAreaHeight - (float)(nextChunkVisibleTime / timeScale * noteAreaHeight);
                        var nextDestRect = new Rectangle(0, nextDestY, textureWidth, (float)(nextChunkVisibleTime / timeScale * noteAreaHeight));

                        Raylib.DrawTexturePro(nextChunk.renderTexture.texture, nextSourceRect, nextDestRect, new Vector2(0, 0), 0, RaylibColor.WHITE);
                    }
                }
            }
        }

        private bool[] GetBlackKeysArray()
        {
            // This should match the logic in RaylibRenderer
            bool[] blackKeys = new bool[257];
            for (int i = 0; i < blackKeys.Length; i++)
            {
                int n = i % 12;
                blackKeys[i] = n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
            }
            return blackKeys;
        }

        private (float left, float right)[] GetKeyPositions()
        {
            // Get the actual key positions from the renderer
            var positions = new (float left, float right)[257];

            for (int i = 0; i < 257; i++)
            {
                positions[i] = renderer.GetKeyPosition(i);
            }

            return positions;
        }

        private void RenderNoteDirectly(float left, float right, float top, float bottom, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Convert normalized coordinates to texture coordinates
            int noteLeft = (int)(left * textureWidth);
            int noteRight = (int)(right * textureWidth);
            int noteTop = (int)top;
            int noteBottom = (int)bottom;

            // Ensure valid dimensions
            int width = noteRight - noteLeft;
            int height = noteBottom - noteTop;

            if (width <= 0 || height <= 0 || noteLeft < 0 || noteRight > textureWidth ||
                noteTop < 0 || noteBottom > CHUNK_HEIGHT) return;

            // Render note with the same visual style as the original renderer
            RenderKivaMIDINoteDirect(noteLeft, noteTop, width, height, colorLeft, colorRight);
        }

        private void RenderKivaMIDINoteDirect(int x, int y, int width, int height, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Simplified version of the note rendering from RaylibRenderer
            // Calculate border thickness
            float noteBorder = 0.00091f;
            int noteBorderH = Math.Max(1, (int)(noteBorder * textureWidth));
            int noteBorderV = Math.Max(1, (int)(noteBorder * CHUNK_HEIGHT));

            // Draw shadow background
            var shadowColorL = new RaylibColor(
                (byte)Math.Max(0, colorLeft.r * 0.2f - 13),
                (byte)Math.Max(0, colorLeft.g * 0.2f - 13),
                (byte)Math.Max(0, colorLeft.b * 0.2f - 13),
                colorLeft.a
            );
            var shadowColorR = new RaylibColor(
                (byte)Math.Max(0, colorRight.r * 0.2f - 13),
                (byte)Math.Max(0, colorRight.g * 0.2f - 13),
                (byte)Math.Max(0, colorRight.b * 0.2f - 13),
                colorRight.a
            );

            // Draw shadow background with gradient
            DrawHorizontalGradientRectangleDirect(x, y, width, height, shadowColorL, shadowColorR);

            // Draw bright inner area
            int borderTop = y + noteBorderV;
            int borderBottom = y + height - noteBorderV;
            int borderLeft = x + noteBorderH;
            int borderRight = x + width - noteBorderH;

            if (borderTop < borderBottom && borderLeft < borderRight)
            {
                var innerColorL = new RaylibColor(
                    (byte)Math.Min(255, colorLeft.r + 25),
                    (byte)Math.Min(255, colorLeft.g + 25),
                    (byte)Math.Min(255, colorLeft.b + 25),
                    colorLeft.a
                );
                var innerColorR = new RaylibColor(
                    (byte)Math.Max(0, colorRight.r - 76),
                    (byte)Math.Max(0, colorRight.g - 76),
                    (byte)Math.Max(0, colorRight.b - 76),
                    colorRight.a
                );

                int innerWidth = borderRight - borderLeft;
                int innerHeight = borderBottom - borderTop;

                DrawHorizontalGradientRectangleDirect(borderLeft, borderTop, innerWidth, innerHeight, innerColorL, innerColorR);
            }
        }

        private void DrawHorizontalGradientRectangleDirect(int x, int y, int width, int height, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // For small rectangles, just use solid color
            if (width <= 4)
            {
                Raylib.DrawRectangle(x, y, width, height, colorLeft);
                return;
            }

            // Draw gradient by drawing vertical strips
            int strips = Math.Min(width, 32);
            float stripWidth = (float)width / strips;

            for (int i = 0; i < strips; i++)
            {
                float t = strips > 1 ? (float)i / (strips - 1) : 0;
                var blendedColor = BlendColorsDirect(colorLeft, colorRight, t);

                int stripX = x + (int)(i * stripWidth);
                int stripW = (int)Math.Ceiling(stripWidth);

                if (stripX + stripW > x + width)
                    stripW = x + width - stripX;

                Raylib.DrawRectangle(stripX, y, stripW, height, blendedColor);
            }
        }

        private RaylibColor BlendColorsDirect(RaylibColor color1, RaylibColor color2, float t)
        {
            return new RaylibColor(
                (byte)(color1.r + (color2.r - color1.r) * t),
                (byte)(color1.g + (color2.g - color1.g) * t),
                (byte)(color1.b + (color2.b - color1.b) * t),
                (byte)(color1.a + (color2.a - color1.a) * t)
            );
        }

        private RaylibColor ExtractColor(uint rgba)
        {
            return new RaylibColor(
                (byte)((rgba >> 24) & 0xFF), // R
                (byte)((rgba >> 16) & 0xFF), // G
                (byte)((rgba >> 8) & 0xFF),  // B
                (byte)(rgba & 0xFF)          // A
            );
        }

        private void DisposeChunks()
        {
            foreach (var chunk in chunks)
            {
                if (chunk.isLoaded)
                {
                    Raylib.UnloadRenderTexture(chunk.renderTexture);
                }
            }
        }

        public void Dispose()
        {
            if (disposed) return;

            DisposeChunks();
            chunks.Clear();
            disposed = true;
        }
    }
}
