﻿<Window x:Class="Kiva_MIDI.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Kiva_MIDI"
        mc:Ignorable="d"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="16"
        TextOptions.TextFormattingMode="Ideal" 
        TextOptions.TextRenderingMode="Auto"        
        Title="SettingsWindow" Height="400" Width="608" WindowStyle="None" WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../ButtonTemplates.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="{Binding ActualHeight,ElementName=titlebar}"/>
    </WindowChrome.WindowChrome>
    <Grid>
        <DockPanel LastChildFill="True">
            <Border Panel.ZIndex="1" Visibility="{Binding ChromeVisibility, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}" Background="#0068c9" DockPanel.Dock="Top" Height="40" x:Name="titlebar">
                <Border.Effect>
                    <DropShadowEffect BlurRadius="5" ShadowDepth="0"/>
                </Border.Effect>
                <Grid>
                    <DockPanel Name="tabPanel" WindowChrome.IsHitTestVisibleInChrome="True">
                        <Button Style="{DynamicResource TransparentButtonStyle}" HorizontalAlignment="Left" Width="87">
                            Visual
                        </Button>
                        <Button Style="{DynamicResource TransparentButtonStyle}" HorizontalAlignment="Left" Width="87">
                            Audio
                        </Button>
                        <Button Style="{DynamicResource TransparentButtonStyle}" HorizontalAlignment="Left" Width="87">
                            Misc
                        </Button>
                    </DockPanel>
                    <DockPanel HorizontalAlignment="Right" Margin="0" Width="40">
                        <Button Style="{StaticResource WindowButton}" Focusable="False" Name="ExitButton" Background="Red" Width="20" Height="20" Margin="3" DockPanel.Dock="Right" WindowChrome.IsHitTestVisibleInChrome="True" Click="ExitButton_Click"></Button>
                    </DockPanel>
                </Grid>
            </Border>
            <Grid Name="content" Background="#0179db">
                <local:VisualSettings x:Name="visualSettings"/>
                <local:AudioSettings x:Name="audioSettings"/>
                <local:MiscSettings x:Name="miscSettings"/>
            </Grid>
        </DockPanel>
    </Grid>
</Window>
