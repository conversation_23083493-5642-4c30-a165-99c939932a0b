@echo off
echo Copying raylib.dll to output directories...

REM Copy to Debug folder
if exist "packages\Raylib-cs.3.1.5\runtimes\win-x64\native\raylib.dll" (
    if not exist "bin\Debug" mkdir "bin\Debug"
    copy "packages\Raylib-cs.3.1.5\runtimes\win-x64\native\raylib.dll" "bin\Debug\raylib.dll"
    echo Copied to bin\Debug\
)

REM Copy to Release folder
if exist "packages\Raylib-cs.3.1.5\runtimes\win-x64\native\raylib.dll" (
    if not exist "bin\Release" mkdir "bin\Release"
    copy "packages\Raylib-cs.3.1.5\runtimes\win-x64\native\raylib.dll" "bin\Release\raylib.dll"
    echo Copied to bin\Release\
)

echo Done!
pause
